import React, { useState } from "react";
import Head from "next/head";
import { Const } from "@/utils/Constants";
import { getSubmenus } from "@/pages/api/CategoryApi";
import { getWebStoriesCategory } from "@/pages/api/WebStoriesApi";

const ampWebStoriesCSS = `
  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  }

  .amp-webstoriesPage_cntr {
    min-height: 100vh;
    background: #000;
    color: #fff;
  }

  .amp-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .amp-hero-section {
    background: #000;
    color: #fff;
    padding: 60px 0 40px;
    text-align: center;
  }

  .amp-hero-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 2px;
  }

  .amp-stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin: 40px 0;
    padding: 20px;
  }

  .amp-story-card {
    position: relative;
    aspect-ratio: 9/16;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    background: #333;
  }

  .amp-story-card:hover {
    transform: scale(1.05);
  }

  .amp-story-player-container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .amp-story-preview {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .amp-story-overlay {
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 20px;
    color: white;
  }

  .amp-story-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.3;
  }

  .amp-story-author {
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .amp-load-more {
    display: flex;
    justify-content: center;
    margin: 40px 0;
  }

  .amp-load-more-button {
    background: #fff;
    color: #000;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .amp-load-more-button:hover {
    background: #f0f0f0;
  }

  .amp-load-more-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    .amp-stories-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    .amp-hero-section h1 {
      font-size: 2rem;
    }
  }

  @media (max-width: 480px) {
    .amp-stories-grid {
      grid-template-columns: 1fr;
    }
  }
`;

const AMPStoryCard = ({ title, image, altName, slug, author = [], contributor = [] }) => {
	const getAuthorText = (prefix, authors, contributors) => {
		const allAuthors = [...authors, ...contributors];
		if (allAuthors.length === 0) return "";
		return `${prefix} ${allAuthors.join(", ")}`;
	};

	return (
		<div className="amp-story-card">
			<div
				className="amp-story-preview"
				style={{ backgroundImage: `url(${image})` }}
				onClick={() => window.open(slug, "_blank")}
			>
				<div className="amp-story-overlay">
					<h2 className="amp-story-title">{title ?? ""}</h2>
					<span className="amp-story-author">{getAuthorText("// By", author, contributor)}</span>
				</div>
			</div>
		</div>
	);
};

const AMPStoryPlayer = ({ stories }) => {
	if (!stories || stories.length === 0) return null;

	return (
		<amp-story-player layout="fixed" width="360" height="600">
			{stories.map((story, index) => (
				<a key={index} href={story.slug}>
					<amp-img
						src={story.image}
						width="360"
						height="600"
						loading="lazy"
						data-amp-story-player-poster-img
						alt={story.altName}
					/>
					{story.title}
				</a>
			))}
		</amp-story-player>
	);
};

const AMPHero = ({ title }) => {
	return (
		<section className="amp-hero-section">
			<h1>{title ?? "AMP Web Stories"}</h1>
		</section>
	);
};

const AMPButton = ({ onClick, children, disabled }) => {
	return (
		<button onClick={onClick} className="amp-load-more-button" disabled={disabled}>
			{children}
		</button>
	);
};

const AMPSeoHeader = ({ meta }) => {
	return (
		<Head>
			<title>{meta?.title || "AMP Web Stories"}</title>
			<meta name="description" content={meta?.description || ""} />
			<meta name="keywords" content={meta?.keywords || ""} />
			<link rel="canonical" href={meta?.canonical || ""} />
			<script async src="https://cdn.ampproject.org/v0.js"></script>
			<script
				async
				custom-element="amp-img"
				src="https://cdn.ampproject.org/v0/amp-img-0.1.js"
			></script>
			<script
				async
				custom-element="amp-story-player"
				src="https://cdn.ampproject.org/v0/amp-story-player-0.1.js"
			></script>
			<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
			<style amp-boilerplate="">{`body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`}</style>
			<noscript>
				<style amp-boilerplate="">{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}</style>
			</noscript>
			<style amp-custom="">{ampWebStoriesCSS}</style>
		</Head>
	);
};

const AMPBreadcrumbSchema = () => {
	const breadcrumbSchema = {
		"@context": "https://schema.org",
		"@type": "BreadcrumbList",
		itemListElement: [
			{
				"@type": "ListItem",
				position: 1,
				name: "Home",
				item: "/",
			},
			{
				"@type": "ListItem",
				position: 2,
				name: "AMP Web Stories",
				item: "/amp-webstories",
			},
		],
	};

	return (
		<script
			type="application/ld+json"
			dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
		/>
	);
};

const AMPWebStoryCategory = ({ meta, title, initialData, initialCount }) => {
	const [data, setData] = useState(initialData);
	const [hasMore, setHasMore] = useState(initialData.length < initialCount);
	const [loading, setLoading] = useState(false);
	const [offset, setOffset] = useState(Const.Limit);

	const handleLoadMore = async () => {
		if (!hasMore || loading) return;

		setLoading(true);

		const payload = {
			slug: `/webstories`,
			limit: Const.Limit,
			offset: offset,
		};

		try {
			const response = await getWebStoriesCategory(payload);
			const newData = response?.data?.data || [];
			const newTotalCount = response?.data?.count || 0;

			setData((prevData) => [...prevData, ...newData]);
			setOffset((prevOffset) => prevOffset + Const.Limit);

			if (data.length + newData.length >= newTotalCount) {
				setHasMore(false);
			}
		} catch (error) {
			console.error("Error loading more data:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<>
			<AMPSeoHeader meta={meta} />
			<AMPBreadcrumbSchema />
			<div className="amp-webstoriesPage_cntr">
				<div className="amp-container">
					<AMPHero title={title} />

					{/* Stories Grid */}
					<div className="amp-stories-grid">
						{data.map((el, index) => (
							<AMPStoryCard
								key={index}
								title={el.title}
								image={el.coverImg}
								altName={el.altName}
								slug={`/amp-webstories${el.slug.replace("/webstories", "")}`}
								author={el.author}
								contributor={el.contributor}
							/>
						))}
					</div>

					{/* Load More Button */}
					{hasMore && (
						<div className="amp-load-more">
							<AMPButton onClick={handleLoadMore} disabled={loading}>
								{loading ? "Loading..." : "SEE MORE"}
							</AMPButton>
						</div>
					)}
				</div>
			</div>
		</>
	);
};

export default AMPWebStoryCategory;

export async function getServerSideProps() {
	const slug = `/webstories`;
	const payload = {
		slug,
		limit: Const.Limit,
		offset: Const.Offset,
	};

	try {
		const [categoryRes, submenusRes] = await Promise.all([
			getWebStoriesCategory(payload),
			getSubmenus(slug),
		]);

		if (!categoryRes?.data?.isExists) {
			return { notFound: true };
		}

		const categoryData = categoryRes?.data?.data || [];
		const categoryCount = categoryRes?.data?.count || 0;
		const breadcrumbs = categoryRes?.data?.breadcrumbs ?? [];
		const submenus = submenusRes?.data?.submenus || [];
		const title = submenusRes?.data?.title || "AMP Web Stories";
		const meta = categoryRes?.data?.meta || {};

		return {
			props: {
				initialData: categoryData,
				initialCount: categoryCount,
				breadcrumbs,
				submenus,
				meta,
				title,
			},
		};
	} catch (error) {
		console.error("Error fetching category data:", error.message);
		return { notFound: true };
	}
}
