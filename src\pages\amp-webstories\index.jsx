import React, { useState } from "react";
import Head from "next/head";
import { Const } from "@/utils/Constants";
import { getSubmenus } from "@/pages/api/CategoryApi";
import { getWebStoriesCategory } from "@/pages/api/WebStoriesApi";

const ampWebStoriesCSS = `
  .amp-webstoriesPage_cntr {
    min-height: 100vh;
    background: #000;
    color: #fff;
  }
  
  .amp-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .amp-hero-section {
    background: #000;
    color: #fff;
    padding: 60px 0 40px;
  }
  
  .amp-Title-header-wrapper {
    text-align: center;
  }
  
  .amp-h1-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 2px;
  }
  
  .amp-webstories_grid_wrapper_desktop {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin: 40px 0;
  }
  
  .amp-webstories_grid_wrapper_mbl {
    display: none;
  }
  
  .amp-webstories_itemCard {
    position: relative;
    aspect-ratio: 9/16;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
  }
  
  .amp-webstories_itemCard:hover {
    transform: scale(1.05);
  }
  
  .amp-relatedCard {
    display: block;
    position: relative;
    width: 100%;
    height: 100%;
    text-decoration: none;
    color: inherit;
  }
  
  .amp-webstories_itemCard figure {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0;
  }
  
  .amp-webstories_itemCard amp-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .amp-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 20px;
    color: white;
  }
  
  .amp-gradient h2 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.3;
  }
  
  .amp-date-author {
    font-size: 0.9rem;
    opacity: 0.8;
  }
  
  .amp-flex-all {
    display: flex;
    justify-content: center;
    margin: 40px 0;
  }
  
  .amp-card-actions_actions {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .amp-card-actions_actions_button {
    display: inline-block;
    background: #fff;
    color: #000;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: background 0.3s ease;
  }
  
  .amp-card-actions_actions_button:hover {
    background: #f0f0f0;
  }
  
  .amp-button_label {
    display: block;
  }
  
  @media (max-width: 768px) {
    .amp-webstories_grid_wrapper_desktop {
      display: none;
    }
    
    .amp-webstories_grid_wrapper_mbl {
      display: block;
    }
    
    .amp-GridCardContainer {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin: 20px 0;
    }
    
    .amp-h1-header h1 {
      font-size: 2rem;
    }
  }
  
  @media (max-width: 480px) {
    .amp-GridCardContainer {
      grid-template-columns: 1fr;
    }
  }
`;

const AMPWebcard = ({ title, image, altName, slug, author = [], contributor = [] }) => {
	const getAuthorText = (prefix, authors, contributors) => {
		const allAuthors = [...authors, ...contributors];
		if (allAuthors.length === 0) return "";
		return `${prefix} ${allAuthors.join(", ")}`;
	};

	return (
		<div className="amp-webstories_itemCard">
			<a href={slug ?? "#"} className="amp-relatedCard">
				<figure>
					<amp-img
						src={image ?? ""}
						alt={altName ?? ""}
						width="300"
						height="533"
						layout="responsive"
					/>
				</figure>
				<div className="amp-gradient">
					<h2>{title ?? ""}</h2>
					<span className="amp-date-author">{getAuthorText("// By", author, contributor)}</span>
				</div>
			</a>
		</div>
	);
};

const AMPHero = ({ title }) => {
	return (
		<section className="amp-hero-section">
			<div className="amp-Title-header-wrapper">
				<div className="amp-h1-header">
					<h1>{title ?? ""}</h1>
				</div>
			</div>
		</section>
	);
};

const AMPButton = ({ onClick, children, disabled }) => {
	return (
		<ul className="amp-card-actions_actions">
			<li>
				<button
					on={onClick ? `tap:${onClick}` : ""}
					className="amp-card-actions_actions_button"
					disabled={disabled}
				>
					<span className="amp-button_label">{children}</span>
				</button>
			</li>
		</ul>
	);
};

const AMPSeoHeader = ({ meta }) => {
	return (
		<Head>
			<title>{meta?.title || "AMP Web Stories"}</title>
			<meta name="description" content={meta?.description || ""} />
			<meta name="keywords" content={meta?.keywords || ""} />
			<link rel="canonical" href={meta?.canonical || ""} />
			<script async src="https://cdn.ampproject.org/v0.js"></script>
			<script
				async
				custom-element="amp-img"
				src="https://cdn.ampproject.org/v0/amp-img-0.1.js"
			></script>
			<script
				async
				custom-element="amp-list"
				src="https://cdn.ampproject.org/v0/amp-list-0.1.js"
			></script>
			<meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1" />
			<style amp-boilerplate="">{`body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`}</style>
			<noscript>
				<style amp-boilerplate="">{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}</style>
			</noscript>
			<style amp-custom="">{ampWebStoriesCSS}</style>
		</Head>
	);
};

const AMPBreadcrumbSchema = () => {
	const breadcrumbSchema = {
		"@context": "https://schema.org",
		"@type": "BreadcrumbList",
		itemListElement: [
			{
				"@type": "ListItem",
				position: 1,
				name: "Home",
				item: "/",
			},
			{
				"@type": "ListItem",
				position: 2,
				name: "AMP Web Stories",
				item: "/amp-webstories",
			},
		],
	};

	return (
		<script
			type="application/ld+json"
			dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbSchema) }}
		/>
	);
};

const AMPWebStoryCategory = ({ meta, title, initialData, initialCount, breadcrumbs, submenus }) => {
	const [data, setData] = useState(initialData);
	const [totalCount, setTotalCount] = useState(initialCount);
	const [hasMore, setHasMore] = useState(initialData.length < initialCount);
	const [loading, setLoading] = useState(false);
	const [offset, setOffset] = useState(Const.Limit);

	const handleLoadMore = async () => {
		if (!hasMore || loading) return;

		setLoading(true);

		const payload = {
			slug: `/webstories`,
			limit: Const.Limit,
			offset: offset,
		};

		try {
			const response = await getWebStoriesCategory(payload);
			const newData = response?.data?.data || [];
			const newTotalCount = response?.data?.count || 0;

			setData((prevData) => [...prevData, ...newData]);
			setTotalCount(newTotalCount);
			setOffset((prevOffset) => prevOffset + Const.Limit);

			if (data.length + newData.length >= newTotalCount) {
				setHasMore(false);
			}
		} catch (error) {
			console.error("Error loading more data:", error);
		} finally {
			setLoading(false);
		}
	};

	// Split data into columns for desktop view
	const columnCount = 3;
	const splitData = Array.from({ length: columnCount }, (_, colIndex) =>
		data.filter((_, itemIndex) => itemIndex % columnCount === colIndex)
	);

	return (
		<>
			<AMPSeoHeader meta={meta} />
			<AMPBreadcrumbSchema />
			<div className="amp-webstoriesPage_cntr">
				<div className="amp-container">
					<AMPHero title={title} />
					{/* Desktop grid view */}
					<div className="amp-webstories_grid_wrapper_desktop">
						{splitData.map((columnData, colIndex) => (
							<div key={colIndex}>
								{columnData.map((el, index) => (
									<AMPWebcard
										key={`${colIndex}-${index}`}
										title={el.title}
										image={el.coverImg}
										altName={el.altName}
										category={el.category}
										slug={`/amp-webstories${el.slug.replace("/webstories", "")}`}
										timestamp={el.timestamp}
										author={el.author}
										contributor={el.contributor}
									/>
								))}
							</div>
						))}
					</div>
					{/* Mobile grid view */}
					<div className="amp-webstories_grid_wrapper_mbl">
						<div className="amp-GridCardContainer">
							{data.map((el, index) => (
								<AMPWebcard
									key={`mbl-${index}`}
									title={el.title}
									image={el.coverImg}
									altName={el.altName}
									category={el.category}
									slug={`/amp-webstories${el.slug.replace("/webstories", "")}`}
									timestamp={el.timestamp}
									author={el.author}
									contributor={el.contributor}
								/>
							))}
						</div>
					</div>
					{/* Load More Button */}
					{hasMore && (
						<div className="amp-flex-all">
							<AMPButton onClick={handleLoadMore} disabled={loading}>
								{loading ? "Loading..." : "SEE MORE"}
							</AMPButton>
						</div>
					)}
				</div>
			</div>
		</>
	);
};

export default AMPWebStoryCategory;

export async function getServerSideProps({ params }) {
	const slug = `/webstories`;
	const payload = {
		slug,
		limit: Const.Limit,
		offset: Const.Offset,
	};

	try {
		const [categoryRes, submenusRes] = await Promise.all([
			getWebStoriesCategory(payload),
			getSubmenus(slug),
		]);

		if (!categoryRes?.data?.isExists) {
			return { notFound: true };
		}

		const categoryData = categoryRes?.data?.data || [];
		const categoryCount = categoryRes?.data?.count || 0;
		const breadcrumbs = categoryRes?.data?.breadcrumbs ?? [];
		const submenus = submenusRes?.data?.submenus || [];
		const title = submenusRes?.data?.title || "AMP Web Stories";
		const meta = categoryRes?.data?.meta || {};

		return {
			props: {
				initialData: categoryData,
				initialCount: categoryCount,
				breadcrumbs,
				submenus,
				meta,
				title,
			},
		};
	} catch (error) {
		console.error("Error fetching category data:", error.message);
		return { notFound: true };
	}
}
